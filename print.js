const puppeteer = require("puppeteer");
const readline = require("readline");
const fs = require("fs");

function askQuestion(query) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => rl.question(query, ans => {
    rl.close();
    resolve(ans);
  }))
}

(async () => {
  console.log("Iniciando o script...");
  console.log("Abrindo o navegador...");

  // Caminhos possíveis para o Chrome
  const chromePaths = [
    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
    process.env.LOCALAPPDATA + "\\Google\\Chrome\\Application\\chrome.exe"
  ];

  let executablePath = null;
  for (const path of chromePaths) {
    try {
      if (fs.existsSync(path)) {
        executablePath = path;
        console.log(`Chrome encontrado em: ${path}`);
        break;
      }
    } catch (err) {
      // Continua tentando outros caminhos
    }
  }

  const launchOptions = {
    headless: false, // precisa ser "false" para permitir clicar nos botões
    args: ["--kiosk-printing"], // impressão silenciosa
  };

  if (executablePath) {
    launchOptions.executablePath = executablePath;
  } else {
    console.log("Chrome não encontrado nos caminhos padrão. Usando Chromium do Puppeteer...");
  }

  try {
    const browser = await puppeteer.launch(launchOptions);
    console.log("Navegador aberto com sucesso!");

    const page = await browser.newPage();
    console.log("Navegando para a página de login...");

    await page.goto("https://doity.com.br/admin/users/login");

    await page.type("#UserUsername", "<EMAIL>");
    await page.type("#UserPassword", "Davicomunic22#");
    await page.click('input[value="Entrar"]');
    await page.waitForNavigation();

    let currentPage = await askQuestion("Digite o número da página para começar: ");
    currentPage = parseInt(currentPage);
    if (isNaN(currentPage) || currentPage < 1) currentPage = 1;

    let hasNext = true;

    while (hasNext) {

      // Navegar para a página atual
      await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, { waitUntil: "networkidle0" });

      // ---- REMOVER CHAT FLUTUANTE ----
      try {
        await page.evaluate(() => {
          const chat = document.querySelector(".live-chat-widget, #chat-widget");
          if (chat) chat.remove();
        });
      } catch (err) {
        console.log("Chat não encontrado ou já removido.");
      }

      // ---- LOOP DE IMPRESSÃO ----
      const printButtons = await page.$$("#bt-imprimir-etiqueta");
      for (let i = 0; i < printButtons.length; i++) {
        const currentButtons = await page.$$("#bt-imprimir-etiqueta");
        const button = currentButtons[i];
        if (!button) continue;

        console.log(`Página ${currentPage} - Imprimindo inscrito ${i + 1}`);
        await page.evaluate((btn) => btn.click(), button);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // ---- PERGUNTAR AO USUÁRIO SE QUER AVANÇAR ----
      const answer = await askQuestion("Pressione ENTER para ir para a próxima página ou digite 'sair' para encerrar: ");
      if (answer.toLowerCase() === "sair") {
        hasNext = false;
      } else {
        currentPage++; // incrementar página manualmente
      }
    }

    await browser.close();
  } catch (error) {
    console.error("Erro ao executar o script:", error);
    process.exit(1);
  }
})();