const puppeteer = require("puppeteer");
const fs = require("fs");

console.log("🚀 Testando configuração do projeto...");

// Testar se o Puppeteer funciona
(async () => {
  try {
    console.log("✅ Puppeteer carregado com sucesso!");
    
    // Verificar caminhos do Chrome
    const chromePaths = [
      "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
      "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
      process.env.LOCALAPPDATA + "\\Google\\Chrome\\Application\\chrome.exe"
    ];

    let chromeFound = false;
    for (const path of chromePaths) {
      if (fs.existsSync(path)) {
        console.log(`✅ Chrome encontrado em: ${path}`);
        chromeFound = true;
        break;
      }
    }

    if (!chromeFound) {
      console.log("⚠️  Chrome não encontrado nos caminhos padrão. Puppeteer usará Chromium próprio.");
    }

    // Testar abertura do navegador
    console.log("🔄 Testando abertura do navegador...");
    const browser = await puppeteer.launch({
      headless: true, // Modo headless para teste
      args: ["--no-sandbox", "--disable-setuid-sandbox"]
    });
    
    const page = await browser.newPage();
    await page.goto("https://www.google.com");
    console.log("✅ Navegador aberto e página carregada com sucesso!");
    
    await browser.close();
    console.log("✅ Teste concluído com sucesso!");
    console.log("\n🎉 Tudo configurado corretamente! Você pode executar o script principal com:");
    console.log("   npm start");
    console.log("   ou");
    console.log("   node print.js");
    
  } catch (error) {
    console.error("❌ Erro durante o teste:", error.message);
    console.log("\n🔧 Possíveis soluções:");
    console.log("1. Verifique se o Node.js está instalado corretamente");
    console.log("2. Execute: npm install");
    console.log("3. Verifique sua conexão com a internet");
  }
})();
